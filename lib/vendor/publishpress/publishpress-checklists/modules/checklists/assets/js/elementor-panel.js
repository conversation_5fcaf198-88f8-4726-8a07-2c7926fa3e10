/**
 * PublishPress Checklists - Elementor Integration
 * Creates a floating checklists panel similar to Elementor's Structure panel
 */

(function ($, window, document) {
  'use strict';

  console.log('PublishPress Checklists: Elementor panel script loaded');

  // Initialize when document is ready, with fallback for Elementor
  $(document).ready(function () {
    console.log('PublishPress Checklists: Document ready, checking for Elementor...');
    console.log('PublishPress Checklists: Current URL:', window.location.href);
    console.log('PublishPress Checklists: Elementor available:', typeof elementor !== 'undefined');

    // Add a simple test icon to see if the script is working at all
    if (window.location.href.indexOf('elementor') !== -1 || window.location.href.indexOf('action=edit') !== -1) {
      $('body').append(
        '<div id="pp-checklists-test" style="position:fixed;top:10px;right:10px;background:red;color:white;padding:5px;z-index:999999;">Checklists Script Loaded</div>',
      );
      setTimeout(function () {
        $('#pp-checklists-test').remove();
      }, 3000);
    }

    // Try to initialize immediately if Elementor is already available
    if (typeof elementor !== 'undefined' && elementor.config) {
      console.log('PublishPress Checklists: Elementor found, initializing...');
      initializeChecklists();
    } else {
      // Wait for Elementor to be ready
      $(window).on('elementor:init', function () {
        console.log('PublishPress Checklists: Elementor init event fired, initializing...');
        initializeChecklists();
      });

      // Fallback: try again after a delay
      setTimeout(function () {
        if (typeof elementor !== 'undefined' && elementor.config && !window.PPChecklistsElementor) {
          console.log('PublishPress Checklists: Fallback initialization...');
          initializeChecklists();
        }
      }, 3000);
    }
  });

  function initializeChecklists() {
    var PPChecklistsElementor = {
      // Panel state
      isOpen: false,
      panel: null,
      requirements: [],
      failedRequirements: [],

      // Initialize the integration
      init: function () {
        console.log('PublishPress Checklists: Initializing Elementor integration');
        this.createFloatingIcon();
        this.createFloatingPanel();
        this.loadRequirements();
        this.bindEvents();

        // Listen for Elementor editor changes if available
        if (typeof elementor !== 'undefined' && elementor.channels && elementor.channels.editor) {
          elementor.channels.editor.on('change', this.handleEditorChange.bind(this));
          console.log('PublishPress Checklists: Listening to Elementor editor changes');
        } else {
          console.log('PublishPress Checklists: Elementor editor channels not available, using fallback');
        }
      },

      // Create the floating icon similar to Structure panel
      createFloatingIcon: function () {
        var iconHtml = `
                    <div id="pp-checklists-elementor-icon" class="pp-checklists-floating-icon" title="Checklists">
                        <i class="eicon-checklist" aria-hidden="true"></i>
                        <span class="pp-checklists-badge" style="display: none;">0</span>
                    </div>
                `;

        // Add icon to body first
        $('body').append(iconHtml);
        console.log('PublishPress Checklists: Icon added to page');

        // Try to position it near Elementor's interface
        setTimeout(function () {
          var $elementorPanel = $('#elementor-panel');
          var $icon = $('#pp-checklists-elementor-icon');

          console.log('PublishPress Checklists: Positioning icon, Elementor panel found:', $elementorPanel.length > 0);

          if ($elementorPanel.length && $icon.length) {
            // Position relative to Elementor panel
            $icon.css({
              position: 'fixed',
              top: '60px',
              right: $elementorPanel.outerWidth() + 20 + 'px',
              zIndex: 999999,
            });
          } else {
            // Fallback positioning
            $icon.addClass('pp-checklists-fallback-position');
          }
        }, 1500);
      },

      // Create the floating panel
      createFloatingPanel: function () {
        var panelHtml = `
                    <div id="pp-checklists-elementor-panel" class="pp-checklists-floating-panel" style="display: none;">
                        <div class="pp-checklists-panel-header">
                            <h3>
                                <i class="eicon-checklist"></i>
                                Checklists
                            </h3>
                            <button class="pp-checklists-close-btn" type="button">
                                <i class="eicon-close"></i>
                            </button>
                        </div>
                        <div class="pp-checklists-panel-content">
                            <div class="pp-checklists-loading">
                                <i class="eicon-loading eicon-animation-spin"></i>
                                Loading checklists...
                            </div>
                            <div class="pp-checklists-requirements-list" style="display: none;">
                                <!-- Requirements will be populated here -->
                            </div>
                            <div class="pp-checklists-empty" style="display: none;">
                                <p><em>No checklist tasks configured.</em></p>
                            </div>
                        </div>
                    </div>
                `;

        $('body').append(panelHtml);
        this.panel = $('#pp-checklists-elementor-panel');
      },

      // Get the current post ID
      getPostId: function () {
        // Try multiple methods to get the post ID
        if (
          typeof elementor !== 'undefined' &&
          elementor.config &&
          elementor.config.document &&
          elementor.config.document.id
        ) {
          return elementor.config.document.id;
        }

        if (typeof ppChecklistsElementor !== 'undefined' && ppChecklistsElementor.postId) {
          return ppChecklistsElementor.postId;
        }

        // Try to get from URL
        var urlParams = new URLSearchParams(window.location.search);
        var postId = urlParams.get('post');
        if (postId) {
          return parseInt(postId);
        }

        // Try to get from global WordPress variables
        if (
          typeof window.pagenow !== 'undefined' &&
          window.pagenow === 'post' &&
          typeof window.typenow !== 'undefined'
        ) {
          var matches = window.location.href.match(/post=(\d+)/);
          if (matches && matches[1]) {
            return parseInt(matches[1]);
          }
        }

        return null;
      },

      // Load requirements from server
      loadRequirements: function () {
        var self = this;

        // Check if requirements are already available globally
        if (typeof ppChecklists !== 'undefined' && ppChecklists.requirements) {
          this.requirements = Object.values(ppChecklists.requirements);
          this.updateBadge();
          return;
        }

        // Load via AJAX if not available
        var postId = this.getPostId();
        if (!postId) {
          console.warn('PublishPress Checklists: Could not determine post ID');
          return;
        }

        $.ajax({
          url: ajaxurl || '/wp-admin/admin-ajax.php',
          type: 'POST',
          data: {
            action: 'pp_checklists_get_requirements',
            post_id: postId,
            nonce: ppChecklistsElementor.nonce || '',
          },
          success: function (response) {
            if (response.success && response.data) {
              // Convert object to array if needed
              if (typeof response.data === 'object' && !Array.isArray(response.data)) {
                self.requirements = Object.values(response.data);
              } else {
                self.requirements = response.data;
              }
              console.log('PublishPress Checklists: Requirements loaded:', self.requirements);
              self.updateBadge();
            }
          },
          error: function () {
            console.warn('PublishPress Checklists: Failed to load requirements');
          },
        });
      },

      // Update the badge count
      updateBadge: function () {
        if (!Array.isArray(this.requirements)) {
          console.warn('PublishPress Checklists: Requirements is not an array:', this.requirements);
          return;
        }

        var failedCount = this.requirements.filter(function (req) {
          return req.rule === 'block' && !req.status;
        }).length;

        var $badge = $('.pp-checklists-badge');
        if (failedCount > 0) {
          $badge.text(failedCount).show();
          $('#pp-checklists-elementor-icon').addClass('has-failed-requirements');
        } else {
          $badge.hide();
          $('#pp-checklists-elementor-icon').removeClass('has-failed-requirements');
        }
      },

      // Bind events
      bindEvents: function () {
        var self = this;

        // Toggle panel on icon click
        $(document).on('click', '#pp-checklists-elementor-icon', function (e) {
          e.preventDefault();
          console.log('PublishPress Checklists: Icon clicked');
          self.togglePanel();
        });

        // Close panel
        $(document).on('click', '.pp-checklists-close-btn', function (e) {
          e.preventDefault();
          self.closePanel();
        });

        // Close panel when clicking outside
        $(document).on('click', function (e) {
          if (
            self.isOpen &&
            !$(e.target).closest('#pp-checklists-elementor-panel').length &&
            !$(e.target).closest('#pp-checklists-elementor-icon').length
          ) {
            self.closePanel();
          }
        });

        // Handle custom item clicks
        $(document).on('click', '.pp-checklists-custom-item .status-label', function () {
          self.toggleCustomItem($(this));
        });

        // Handle requirement buttons
        $(document).on('click', '.pp-checklists-check-item', function (e) {
          e.preventDefault();
          self.handleRequirementButton($(this));
        });
      },

      // Toggle panel visibility
      togglePanel: function () {
        console.log('PublishPress Checklists: togglePanel called, isOpen:', this.isOpen);
        if (this.isOpen) {
          this.closePanel();
        } else {
          this.openPanel();
        }
      },

      // Open the panel
      openPanel: function () {
        console.log('PublishPress Checklists: openPanel called');
        if (!this.panel) {
          console.log('PublishPress Checklists: Panel not found, creating...');
          this.createFloatingPanel();
        }

        console.log('PublishPress Checklists: Showing panel, panel exists:', !!this.panel);
        this.panel.show();
        this.isOpen = true;
        this.populatePanel();

        // Position the panel
        this.positionPanel();
      },

      // Close the panel
      closePanel: function () {
        if (this.panel) {
          this.panel.hide();
        }
        this.isOpen = false;
      },

      // Position the panel relative to the icon
      positionPanel: function () {
        if (!this.panel) return;

        var $icon = $('#pp-checklists-elementor-icon');
        if ($icon.length) {
          var iconOffset = $icon.offset();
          var iconHeight = $icon.outerHeight();

          this.panel.css({
            position: 'fixed',
            top: iconOffset.top + iconHeight + 10,
            left: iconOffset.left - 250, // Panel width is ~300px, so offset to align right
            zIndex: 999999,
          });
        }
      },

      // Populate the panel with requirements
      populatePanel: function () {
        var $content = this.panel.find('.pp-checklists-requirements-list');
        var $loading = this.panel.find('.pp-checklists-loading');
        var $empty = this.panel.find('.pp-checklists-empty');

        $loading.hide();

        if (!Array.isArray(this.requirements)) {
          console.warn('PublishPress Checklists: Requirements is not an array:', this.requirements);
          $empty.show();
          $content.hide();
          return;
        }

        if (this.requirements.length === 0) {
          $empty.show();
          $content.hide();
          return;
        }

        $empty.hide();
        $content.show();

        var html = '<ul class="pp-checklists-req-list">';
        var showRequiredLegend = false;

        this.requirements.forEach(function (req, index) {
          var statusClass = req.status ? 'status-yes' : 'status-no';
          var iconClass = req.is_custom
            ? req.status
              ? 'dashicons-yes'
              : ''
            : req.status
            ? 'dashicons-yes'
            : 'dashicons-no';

          if (req.rule === 'block') {
            showRequiredLegend = true;
          }

          html += `
                        <li class="pp-checklists-req pp-checklists-${req.rule} ${statusClass} ${
            req.is_custom ? 'pp-checklists-custom-item' : ''
          }"
                            data-id="${req.id || index}"
                            data-type="${req.type || ''}"
                            data-source="${req.source || ''}"
                            data-extra="${req.extra || ''}">

                            <div class="status-icon dashicons ${iconClass}"></div>
                            <div class="status-label">
                                <span class="req-label">${req.label}</span>
                                ${req.rule === 'block' ? '<span class="required">*</span>' : ''}
                                ${
                                  req.require_button
                                    ? `
                                    <button type="button" class="button button-secondary pp-checklists-check-item">
                                        Check Now
                                        <span class="spinner"></span>
                                    </button>
                                    <div class="request-response"></div>
                                `
                                    : ''
                                }
                            </div>
                        </li>
                    `;
        });

        html += '</ul>';

        if (showRequiredLegend) {
          html += '<div class="pp-checklists-legend"><em>(*) Required</em></div>';
        }

        $content.html(html);
      },

      // Handle editor changes to update requirements
      handleEditorChange: function () {
        var self = this;

        // Debounce the update to avoid too many calls
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(function () {
          self.updateRequirements();
        }, 500);
      },

      // Update requirements status
      updateRequirements: function () {
        var self = this;

        // Trigger requirement validation similar to Gutenberg
        if (typeof PP_Checklists !== 'undefined') {
          $(document).trigger(PP_Checklists.EVENT_TIC);
        }

        // Update badge and panel if open
        setTimeout(function () {
          self.updateBadge();
          if (self.isOpen) {
            self.populatePanel();
          }
        }, 100);
      },

      // Toggle custom item status
      toggleCustomItem: function ($element) {
        var $li = $element.closest('li');
        var reqId = $li.data('id');
        var currentStatus = $li.hasClass('status-yes');
        var newStatus = !currentStatus;

        // Update UI immediately
        $li.removeClass('status-yes status-no').addClass(newStatus ? 'status-yes' : 'status-no');
        var $icon = $li.find('.status-icon');
        $icon.removeClass('dashicons-yes dashicons-no');
        if (newStatus) {
          $icon.addClass('dashicons-yes');
        }

        // Update the requirement in our array
        var req = this.requirements.find(function (r) {
          return r.id == reqId;
        });
        if (req) {
          req.status = newStatus;
        }

        // Update badge
        this.updateBadge();

        // Save to server
        this.saveCustomItemStatus(reqId, newStatus);
      },

      // Save custom item status to server
      saveCustomItemStatus: function (reqId, status) {
        var postId = this.getPostId();
        if (!postId) {
          console.warn('PublishPress Checklists: Could not determine post ID for saving');
          return;
        }

        $.ajax({
          url: ajaxurl || '/wp-admin/admin-ajax.php',
          type: 'POST',
          data: {
            action: 'pp_checklists_update_custom_item',
            post_id: postId,
            item_id: reqId,
            status: status ? 'yes' : 'no',
            nonce: ppChecklistsElementor.nonce || '',
          },
          error: function () {
            console.warn('PublishPress Checklists: Failed to save custom item status');
          },
        });
      },

      // Handle requirement button clicks
      handleRequirementButton: function ($button) {
        var $li = $button.closest('li');
        var reqId = $li.data('id');
        var reqType = $li.data('type');
        var $spinner = $button.find('.spinner');
        var $response = $li.find('.request-response');

        $button.prop('disabled', true);
        $spinner.addClass('is-active');
        $response.empty();

        var postId = this.getPostId();
        if (!postId) {
          console.warn('PublishPress Checklists: Could not determine post ID for requirement check');
          $button.prop('disabled', false);
          $spinner.removeClass('is-active');
          $response.html('<span class="error">Could not determine post ID</span>');
          return;
        }

        $.ajax({
          url: ajaxurl || '/wp-admin/admin-ajax.php',
          type: 'POST',
          data: {
            action: 'pp_checklists_check_requirement',
            post_id: postId,
            requirement_id: reqId,
            requirement_type: reqType,
            nonce: ppChecklistsElementor.nonce || '',
          },
          success: function (response) {
            if (response.success) {
              $response.html('<span class="success">' + (response.data.message || 'Check completed') + '</span>');
              // Update requirement status if provided
              if (typeof response.data.status !== 'undefined') {
                var newStatus = response.data.status;
                $li.removeClass('status-yes status-no').addClass(newStatus ? 'status-yes' : 'status-no');

                // Update in our array
                var req = this.requirements.find(function (r) {
                  return r.id == reqId;
                });
                if (req) {
                  req.status = newStatus;
                }
                this.updateBadge();
              }
            } else {
              $response.html('<span class="error">' + (response.data || 'Check failed') + '</span>');
            }
          }.bind(this),
          error: function () {
            $response.html('<span class="error">Request failed</span>');
          },
          complete: function () {
            $button.prop('disabled', false);
            $spinner.removeClass('is-active');
          },
        });
      },
    };

    // Initialize when Elementor editor is ready
    if (elementor && elementor.config && elementor.config.document) {
      PPChecklistsElementor.init();
    }

    // Make it globally available
    window.PPChecklistsElementor = PPChecklistsElementor;
  }
})(jQuery, window, document);
