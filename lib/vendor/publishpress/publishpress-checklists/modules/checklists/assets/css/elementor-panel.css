/**
 * PublishPress Checklists - Elementor Integration Styles
 * Styles for the floating checklists panel in Elementor editor
 */

/* Floating Icon */
#pp-checklists-elementor-icon {
    position: fixed;
    top: 50px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: #71d7f7;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 999998;
    color: #fff;
    font-size: 16px;
}

#pp-checklists-elementor-icon:hover {
    background: #5bc0de;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#pp-checklists-elementor-icon.has-failed-requirements {
    background: #d9534f;
}

#pp-checklists-elementor-icon.has-failed-requirements:hover {
    background: #c9302c;
}

/* Fallback positioning when can't find Elementor header */
#pp-checklists-elementor-icon.pp-checklists-fallback-position {
    top: 50px;
    right: 20px;
}

/* Badge for failed requirements count */
.pp-checklists-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #d9534f;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Floating Panel */
#pp-checklists-elementor-panel {
    position: fixed;
    width: 320px;
    max-height: 500px;
    background: #fff;
    border: 1px solid #d5dadf;
    border-radius: 3px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    font-size: 13px;
    line-height: 1.4;
    z-index: 999999;
}

/* Panel Header */
.pp-checklists-panel-header {
    background: #f1f3f4;
    border-bottom: 1px solid #d5dadf;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 3px 3px 0 0;
}

.pp-checklists-panel-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495157;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pp-checklists-panel-header h3 i {
    color: #71d7f7;
    font-size: 16px;
}

.pp-checklists-close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    color: #a4afb7;
    font-size: 16px;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.pp-checklists-close-btn:hover {
    background: #e6e9ec;
    color: #495157;
}

/* Panel Content */
.pp-checklists-panel-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.pp-checklists-loading {
    padding: 30px 20px;
    text-align: center;
    color: #a4afb7;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.pp-checklists-loading i {
    font-size: 16px;
}

.pp-checklists-empty {
    padding: 30px 20px;
    text-align: center;
    color: #a4afb7;
}

/* Requirements List */
.pp-checklists-req-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.pp-checklists-req {
    display: flex;
    align-items: flex-start;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
    gap: 12px;
}

.pp-checklists-req:last-child {
    border-bottom: none;
}

.pp-checklists-req:hover {
    background: #f8f9fa;
}

.pp-checklists-req.pp-checklists-custom-item {
    cursor: pointer;
}

.pp-checklists-req.pp-checklists-custom-item:hover {
    background: #f0f8ff;
}

/* Status Icons */
.pp-checklists-req .status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
    margin-top: 2px;
}

.pp-checklists-req.status-yes .status-icon {
    background: #5cb85c;
    color: white;
}

.pp-checklists-req.status-no .status-icon {
    background: #d9534f;
    color: white;
}

.pp-checklists-req.pp-checklists-custom-item.status-no .status-icon {
    background: #e6e9ec;
    color: #a4afb7;
}

/* Status Label */
.pp-checklists-req .status-label {
    flex: 1;
    min-width: 0;
}

.pp-checklists-req .req-label {
    display: block;
    color: #495157;
    font-weight: 500;
    line-height: 1.4;
    word-wrap: break-word;
}

.pp-checklists-req .required {
    color: #d9534f;
    font-weight: bold;
    margin-left: 4px;
}

/* Requirement Buttons */
.pp-checklists-req .pp-checklists-check-item {
    margin-top: 8px;
    padding: 6px 12px;
    font-size: 12px;
    border: 1px solid #71d7f7;
    background: #fff;
    color: #71d7f7;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.pp-checklists-req .pp-checklists-check-item:hover {
    background: #71d7f7;
    color: #fff;
}

.pp-checklists-req .pp-checklists-check-item:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.pp-checklists-req .spinner {
    display: none;
    width: 16px;
    height: 16px;
    margin-left: 5px;
}

.pp-checklists-req .spinner.is-active {
    display: inline-block;
}

/* Request Response */
.pp-checklists-req .request-response {
    margin-top: 8px;
    font-size: 12px;
}

.pp-checklists-req .request-response .success {
    color: #5cb85c;
}

.pp-checklists-req .request-response .error {
    color: #d9534f;
}

/* Legend */
.pp-checklists-legend {
    padding: 15px 20px;
    border-top: 1px solid #f1f3f4;
    background: #f8f9fa;
    color: #6c757d;
    font-size: 12px;
    border-radius: 0 0 3px 3px;
}

/* Rule-specific styles */
.pp-checklists-req.pp-checklists-block {
    border-left: 3px solid #d9534f;
}

.pp-checklists-req.pp-checklists-recommended {
    border-left: 3px solid #f0ad4e;
}

.pp-checklists-req.pp-checklists-disabled {
    opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    #pp-checklists-elementor-panel {
        width: 280px;
        max-height: 400px;
    }
    
    #pp-checklists-elementor-icon {
        right: 10px;
    }
}

/* Animation for panel appearance */
#pp-checklists-elementor-panel {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

#pp-checklists-elementor-panel[style*="display: block"],
#pp-checklists-elementor-panel[style*="display: flex"] {
    opacity: 1;
    transform: translateY(0);
}

/* Scrollbar styling for webkit browsers */
.pp-checklists-panel-content::-webkit-scrollbar {
    width: 6px;
}

.pp-checklists-panel-content::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb {
    background: #d5dadf;
    border-radius: 3px;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb:hover {
    background: #a4afb7;
}
