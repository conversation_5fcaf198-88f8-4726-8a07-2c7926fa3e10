<?php

namespace PublishPressChecklists\Modules\Elementor;

use PublishPress\Checklists\Core\Legacy\Module;
use PublishPress\Checklists\Core\Legacy\Util;
use PublishPress\ChecklistsPro\Factory;
use PublishPress\ChecklistsPro\HooksAbstract as PPCHPROHooksAbstract;
use WPPF\Plugin\ServicesAbstract;
use WPPF\WP\HooksAbstract as WPHooksAbstract;
use WPPF\WP\HooksHandlerInterface;

defined('ABSPATH') or die('No direct script access allowed.');

/**
 * Class PPCHPRO_Elementor
 */
class PPCHPRO_Elementor extends Module
{
    public $module_name = 'elementor';

    /**
     * Instance for the module
     *
     * @var stdClass
     */
    public $module;

    /**
     * Module url
     *
     * @var string
     */
    public $module_url;

    /**
     * @var \PublishPress\Checklists\Core\Legacy\LegacyPlugin
     */
    private $legacyPlugin;

    /**
     * @var HooksHandlerInterface
     */
    private $hooksHandler;

    /**
     * Construct the PPCHPRO_Elementor class
     */
    public function __construct()
    {
        $container = Factory::getContainer();

        $this->legacyPlugin = $container->get(ServicesAbstract::LEGACY_PLUGIN);
        $this->hooksHandler = $container->get(ServicesAbstract::HOOKS_HANDLER);

        $this->module_url = $this->getModuleUrl(__FILE__);

        // Register the module with PublishPress
        $args = [
            'title'                => esc_html__(
                'Elementor Integration',
                'publishpress-checklists-pro'
            ),
            'short_description'    => esc_html__(
                'Integrate checklists with Elementor editor',
                'publishpress-checklists-pro'
            ),
            'extended_description' => esc_html__(
                'Adds a floating checklist panel to the Elementor editor with real-time validation',
                'publishpress-checklists-pro'
            ),
            'module_url'           => $this->module_url,
            'icon_class'           => 'dashicons dashicons-elementor',
            'slug'                 => 'elementor',
            'default_options'      => [
                'enabled' => 'on',
            ],
            'options_page'         => false,
            'autoload'             => true,
        ];

        // Apply a filter to the default options
        $args['default_options'] = $this->hooksHandler->applyFilters(
            PPCHPROHooksAbstract::FILTER_ELEMENTOR_DEFAULT_OPTIONS,
            $args['default_options']
        );

        $this->module = $this->legacyPlugin->register_module($this->module_name, $args);

        $this->hooksHandler->addAction(PPCHPROHooksAbstract::ACTION_CHECKLIST_LOAD_ADDONS, [$this, 'actionLoadAddons']);
    }

    /**
     * Initialize the module. Conditionally loads if the module is enabled
     */
    public function init()
    {
        // Only initialize if Elementor is active
        if (!$this->isElementorActive()) {
            return;
        }

        // Initialize the integration
        $integration = new ElementorIntegration();
        $integration->initHooks();
    }

    /**
     * Action triggered before load requirements. We use this
     * to load the filters.
     */
    public function actionLoadAddons()
    {
        // Only load if Elementor is active
        if (!$this->isElementorActive()) {
            return;
        }

        // Add any filters or actions needed for the integration
        add_filter('publishpress_checklists_post_type_requirements', [$this, 'filterPostTypeRequirements'], 10, 2);
    }

    /**
     * Filter post type requirements for Elementor
     */
    public function filterPostTypeRequirements($requirements, $post_type)
    {
        // Add any Elementor-specific requirements if needed
        return $requirements;
    }

    /**
     * Check if Elementor is active
     */
    private function isElementorActive()
    {
        return defined('ELEMENTOR_VERSION') && class_exists('\Elementor\Plugin');
    }

}
