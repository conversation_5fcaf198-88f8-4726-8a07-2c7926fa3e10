/**
 * PublishPress Checklists - Elementor Panel Styles
 */

/* Header Icon */
.pp-checklists-header-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  margin: 0 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pp-checklists-header-icon:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.pp-checklists-header-icon:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.2);
}

.pp-checklists-header-icon svg {
  color: #a4afb7;
  width: 18px;
  height: 18px;
  pointer-events: none;
  transition: color 0.2s ease;
}

.pp-checklists-header-icon:hover svg {
  color: #ffffff;
}

.pp-checklists-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #dc3232;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border: 2px solid #1e1e1e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Floating Panel */
.pp-checklists-floating-panel {
  position: fixed;
  top: 110px;
  left: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: none;
}

/* Panel Header */
.pp-checklists-panel-header {
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px 4px 0 0;
}

.pp-checklists-panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pp-checklists-panel-header h3 i {
  color: #007cba;
}

.pp-checklists-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  color: #666;
  transition: all 0.2s ease;
}

.pp-checklists-close-btn:hover {
  background: #e0e0e0;
  color: #333;
}

/* Panel Content */
.pp-checklists-panel-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.pp-checklists-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.pp-checklists-loading i {
  margin-right: 8px;
  color: #007cba;
}

.pp-checklists-empty {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Requirements List */
.pp-checklists-req-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.pp-checklists-req {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.pp-checklists-req:last-child {
  border-bottom: none;
}

.pp-checklists-req .status-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-top: 2px;
}

.pp-checklists-req.status-yes .status-icon {
  background: #46b450;
  color: white;
}

.pp-checklists-req.status-no .status-icon {
  background: #dc3232;
  color: white;
}

.pp-checklists-req .status-label {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.pp-checklists-req .req-label {
  color: #333;
}

.pp-checklists-req .required {
  color: #dc3232;
  font-weight: bold;
  margin-left: 4px;
}

/* Legend */
.pp-checklists-legend {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .pp-checklists-floating-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .pp-checklists-floating-icon {
    top: 50px;
    left: 10px;
    width: 35px;
    height: 35px;
  }

  .pp-checklists-floating-icon i {
    font-size: 16px;
  }

  .pp-checklists-floating-panel {
    top: 95px;
    left: 10px;
    width: 280px;
    max-height: 400px;
  }
}

/* Animation for panel show/hide */
.pp-checklists-floating-panel {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.pp-checklists-floating-panel[style*='display: block'] {
  opacity: 1;
  transform: translateY(0);
}

/* Scrollbar styling for webkit browsers */
.pp-checklists-panel-content::-webkit-scrollbar {
  width: 6px;
}

.pp-checklists-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
