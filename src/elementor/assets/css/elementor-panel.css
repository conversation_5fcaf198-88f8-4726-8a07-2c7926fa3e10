/**
 * PublishPress Checklists - Elementor Panel Styles
 */

/* Floating Icon */
.pp-checklists-floating-icon {
  position: fixed;
  top: 60px;
  left: 20px;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  z-index: 999998;
  box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease;
  user-select: none;
  border: 2px solid rgba(255, 255, 255, 0.2);
  will-change: transform;
}

.pp-checklists-floating-icon:hover:not(.pp-checklists-dragging) {
  background: linear-gradient(135deg, #005a87 0%, #004066 100%);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 124, 186, 0.4);
}

.pp-checklists-floating-icon.pp-checklists-dragging {
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(0, 124, 186, 0.5);
  cursor: grabbing !important;
  transition: none !important;
  z-index: 999999;
}

/* Prevent text selection during drag */
body.pp-checklists-dragging-active {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

body.pp-checklists-dragging-active * {
  cursor: grabbing !important;
}

.pp-checklists-floating-icon svg {
  color: white;
  width: 22px;
  height: 22px;
  pointer-events: none;
}

.pp-checklists-drag-handle {
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1;
  pointer-events: none;
  transform: rotate(90deg);
}

.pp-checklists-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #dc3232;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* Floating Panel */
.pp-checklists-floating-panel {
  position: fixed;
  top: 110px;
  left: 20px;
  width: 350px;
  max-height: 500px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: none;
}

/* Panel Header */
.pp-checklists-panel-header {
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px 4px 0 0;
}

.pp-checklists-panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pp-checklists-panel-header h3 i {
  color: #007cba;
}

.pp-checklists-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  color: #666;
  transition: all 0.2s ease;
}

.pp-checklists-close-btn:hover {
  background: #e0e0e0;
  color: #333;
}

/* Panel Content */
.pp-checklists-panel-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.pp-checklists-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.pp-checklists-loading i {
  margin-right: 8px;
  color: #007cba;
}

.pp-checklists-empty {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* Requirements List */
.pp-checklists-req-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.pp-checklists-req {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.pp-checklists-req:last-child {
  border-bottom: none;
}

.pp-checklists-req .status-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-top: 2px;
}

.pp-checklists-req.status-yes .status-icon {
  background: #46b450;
  color: white;
}

.pp-checklists-req.status-no .status-icon {
  background: #dc3232;
  color: white;
}

.pp-checklists-req .status-label {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.pp-checklists-req .req-label {
  color: #333;
}

.pp-checklists-req .required {
  color: #dc3232;
  font-weight: bold;
  margin-left: 4px;
}

/* Legend */
.pp-checklists-legend {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .pp-checklists-floating-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .pp-checklists-floating-icon {
    top: 50px;
    left: 10px;
    width: 35px;
    height: 35px;
  }

  .pp-checklists-floating-icon i {
    font-size: 16px;
  }

  .pp-checklists-floating-panel {
    top: 95px;
    left: 10px;
    width: 280px;
    max-height: 400px;
  }
}

/* Animation for panel show/hide */
.pp-checklists-floating-panel {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.pp-checklists-floating-panel[style*='display: block'] {
  opacity: 1;
  transform: translateY(0);
}

/* Scrollbar styling for webkit browsers */
.pp-checklists-panel-content::-webkit-scrollbar {
  width: 6px;
}

.pp-checklists-panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pp-checklists-panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
