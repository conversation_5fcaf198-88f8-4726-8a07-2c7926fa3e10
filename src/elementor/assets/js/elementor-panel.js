/**
 * PublishPress Checklists - Elementor Panel Integration
 */
(function ($) {
  'use strict';

  class PublishPressChecklistsElementor {
    constructor() {
      this.panel = null;
      this.icon = null;
      this.isOpen = false;
      this.requirements = [];
      this.validationTimer = null;

      this.init();
    }

    init() {
      console.log('PublishPress Checklists: Elementor panel script loaded');

      // Wait for <PERSON>ementor to be ready
      if (typeof elementor !== 'undefined') {
        this.initializeWhenReady();
      } else {
        $(document).ready(() => {
          this.initializeWhenReady();
        });
      }
    }

    initializeWhenReady() {
      console.log('PublishPress Checklists: Document ready, checking for Elementor...');
      console.log('PublishPress Checklists: Current URL:', window.location.href);
      console.log('PublishPress Checklists: Elementor available:', typeof elementor !== 'undefined');

      if (typeof elementor === 'undefined') {
        console.log('PublishPress Checklists: Elementor not found, retrying...');
        setTimeout(() => this.initializeWhenReady(), 1000);
        return;
      }

      console.log('PublishPress Checklists: Elementor found, initializing...');
      this.initializeElementorIntegration();
    }

    initializeElementorIntegration() {
      // Create the floating icon
      this.createFloatingIcon();

      // Create the panel
      this.createPanel();

      // Load requirements
      this.loadRequirements();

      // Set up validation listeners
      this.setupValidationListeners();

      console.log('PublishPress Checklists: Initializing Elementor integration');
    }

    createFloatingIcon() {
      // Remove existing icon if present
      $('#pp-checklists-elementor-icon').remove();

      // Create the header icon
      this.icon = $(`
                <div id="pp-checklists-elementor-icon" class="pp-checklists-header-icon" title="PublishPress Checklists">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
                        <rect x="3" y="6" width="18" height="2" fill="currentColor" opacity="0.7"/>
                        <rect x="3" y="11" width="18" height="2" fill="currentColor" opacity="0.7"/>
                        <rect x="3" y="16" width="12" height="2" fill="currentColor" opacity="0.7"/>
                    </svg>
                    <span class="pp-checklists-badge" style="display: none;">0</span>
                </div>
            `);

      // Add click handler
      this.icon.on('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('PublishPress Checklists: Icon clicked');
        this.togglePanel();
      });

      // Add to page
      $('body').append(this.icon);
      console.log('PublishPress Checklists: Icon added to page');

      // Position the icon
      this.positionIcon();
    }

    positionIcon() {
      // Try to use Elementor's API first
      if (this.tryElementorAPI()) {
        return;
      }

      // Try to find specific Elementor elements for better positioning
      const elementorStructure = $('#elementor-panel-header-menu-button');
      const elementorNavigator = $('#elementor-navigator');
      const elementorTopBar = $('#elementor-editor-wrapper .elementor-editor-header');

      console.log('PublishPress Checklists: Looking for Elementor elements...');
      console.log('Structure button found:', elementorStructure.length > 0);
      console.log('Navigator found:', elementorNavigator.length > 0);
      console.log('Top bar found:', elementorTopBar.length > 0);

      // Try to position next to structure button (top-left area)
      if (elementorStructure.length > 0) {
        this.positionNearStructure(elementorStructure);
      } else if (elementorTopBar.length > 0) {
        this.positionInTopBar(elementorTopBar);
      } else {
        // Fallback to fixed position in top-left
        this.icon.css({
          position: 'fixed',
          top: '10px',
          left: '60px', // After Elementor logo
          zIndex: 999998,
        });
      }
    }

    tryElementorAPI() {
      // Check if we can use Elementor's API to add to header
      if (typeof elementor !== 'undefined' && elementor.modules && elementor.modules.layouts) {
        try {
          // Try to find header layout and add our button
          console.log('PublishPress Checklists: Attempting to use Elementor API...');
          // This would be the ideal way but Elementor doesn't expose this easily
          return false;
        } catch (e) {
          console.log('PublishPress Checklists: Elementor API not available');
          return false;
        }
      }
      return false;
    }

    positionNearStructure(structureButton) {
      // Position next to the structure button in the top-left
      const structureParent = structureButton.parent();

      this.icon.css({
        position: 'relative',
        display: 'inline-flex',
        margin: '0 4px',
        zIndex: 'auto',
      });

      // Insert after the structure button
      structureButton.after(this.icon);
      console.log('PublishPress Checklists: Icon positioned near structure button');
    }

    positionInTopBar(topBar) {
      // Position in the top bar but away from publish button
      this.icon.css({
        position: 'absolute',
        top: '50%',
        left: '60px', // After logo, before other elements
        transform: 'translateY(-50%)',
        zIndex: 999998,
      });

      topBar.append(this.icon);
      console.log('PublishPress Checklists: Icon positioned in top bar');
    }



    createPanel() {
      // Remove existing panel if present
      $('#pp-checklists-elementor-panel').remove();

      // Create the panel HTML
      this.panel = $(`
                <div id="pp-checklists-elementor-panel" class="pp-checklists-floating-panel">
                    <div class="pp-checklists-panel-header">
                        <h3>
                            <i class="eicon-checklist"></i>
                            ${ppChecklistsElementor.strings.checklists}
                        </h3>
                        <button class="pp-checklists-close-btn" type="button">
                            <i class="eicon-close"></i>
                        </button>
                    </div>
                    <div class="pp-checklists-panel-content">
                        <div class="pp-checklists-loading">
                            <i class="eicon-loading eicon-animation-spin"></i>
                            ${ppChecklistsElementor.strings.loading}
                        </div>
                        <div class="pp-checklists-requirements-list" style="display: none;"></div>
                        <div class="pp-checklists-empty" style="display: none;">
                            <p><em>${ppChecklistsElementor.strings.noTasks}</em></p>
                        </div>
                    </div>
                </div>
            `);

      // Add close button handler
      this.panel.find('.pp-checklists-close-btn').on('click', () => {
        this.closePanel();
      });

      // Add to page
      $('body').append(this.panel);

      // Position the panel
      this.positionPanel();
    }

    positionPanel() {
      this.updatePanelPosition();
      this.panel.css({
        zIndex: 999999,
        display: 'none',
      });
    }

    updatePanelPosition() {
      if (!this.panel || !this.icon) return;

      const iconPos = this.icon.position();
      const iconHeight = this.icon.outerHeight();
      const iconWidth = this.icon.outerWidth();
      const panelWidth = this.panel.outerWidth();
      const panelHeight = this.panel.outerHeight();
      const windowWidth = $(window).width();
      const windowHeight = $(window).height();

      let panelTop = iconPos.top + iconHeight + 10;
      let panelLeft = iconPos.left;

      // Adjust if panel would go off-screen
      if (panelLeft + panelWidth > windowWidth) {
        panelLeft = windowWidth - panelWidth - 10;
      }

      if (panelTop + panelHeight > windowHeight) {
        panelTop = iconPos.top - panelHeight - 10;
      }

      // Ensure panel doesn't go off-screen
      panelTop = Math.max(10, panelTop);
      panelLeft = Math.max(10, panelLeft);

      this.panel.css({
        position: 'fixed',
        top: panelTop + 'px',
        left: panelLeft + 'px',
      });
    }

    togglePanel() {
      console.log('PublishPress Checklists: togglePanel called, isOpen:', this.isOpen);

      if (this.isOpen) {
        this.closePanel();
      } else {
        this.openPanel();
      }
    }

    openPanel() {
      console.log('PublishPress Checklists: openPanel called');
      console.log('PublishPress Checklists: Showing panel, panel exists:', !!this.panel);

      this.panel.show();
      this.panel.css('display', 'block');
      this.isOpen = true;

      // Refresh requirements when opening
      this.loadRequirements();
    }

    closePanel() {
      console.log('PublishPress Checklists: closePanel called');

      this.panel.hide();
      this.isOpen = false;
    }

    loadRequirements() {
      console.log('PublishPress Checklists: Loading requirements...');

      // Show loading state
      this.panel.find('.pp-checklists-loading').show();
      this.panel.find('.pp-checklists-requirements-list').hide();
      this.panel.find('.pp-checklists-empty').hide();

      // Make AJAX request to get requirements
      $.ajax({
        url: ppChecklistsElementor.ajaxUrl,
        type: 'POST',
        data: {
          action: 'pp_checklists_elementor_get_requirements',
          nonce: ppChecklistsElementor.nonce,
          post_id: ppChecklistsElementor.postId,
        },
        success: (response) => {
          console.log('PublishPress Checklists: Requirements loaded:', response.data);
          this.requirements = response.data || [];
          this.renderRequirements();
          this.validateRequirements();
        },
        error: (xhr, status, error) => {
          console.error('PublishPress Checklists: Error loading requirements:', error);
          this.showError('Failed to load requirements');
        },
      });
    }

    renderRequirements() {
      const $list = this.panel.find('.pp-checklists-requirements-list');
      const $loading = this.panel.find('.pp-checklists-loading');
      const $empty = this.panel.find('.pp-checklists-empty');

      $loading.hide();

      // Check if requirements is an object or array
      const requirementsArray = Array.isArray(this.requirements)
        ? this.requirements
        : Object.entries(this.requirements || {});

      if (!this.requirements || requirementsArray.length === 0) {
        $empty.show();
        return;
      }

      // Build requirements HTML
      let html = '<ul class="pp-checklists-req-list">';

      requirementsArray.forEach((item, index) => {
        // Handle both array and object formats
        let req, reqId;
        if (Array.isArray(this.requirements)) {
          req = item;
          reqId = req.id || index;
        } else {
          reqId = item[0]; // key
          req = item[1]; // value
        }

        // Map status: true/false to passed/failed for consistency
        const status = req.status === true || req.status === 'passed' ? 'passed' : 'failed';
        const statusClass = status === 'passed' ? 'status-yes' : 'status-no';
        const statusIcon = status === 'passed' ? 'dashicons-yes' : 'dashicons-no';

        // Check if required based on rule (block = required)
        const isRequired = req.rule === 'block';
        const requiredMark = isRequired ? '<span class="required">*</span>' : '';

        html += `
                    <li class="pp-checklists-req ${statusClass}" data-id="${reqId}" data-type="${req.type || ''}">
                        <div class="status-icon dashicons ${statusIcon}"></div>
                        <div class="status-label">
                            <span class="req-label">${req.label || 'Unknown requirement'}</span>
                            ${requiredMark}
                        </div>
                    </li>
                `;
      });

      html += '</ul>';
      html += '<div class="pp-checklists-legend"><em>(*) Required</em></div>';

      $list.html(html).show();

      // Update badge count
      this.updateBadgeCount();
    }

    updateBadgeCount() {
      let failedCount = 0;

      if (Array.isArray(this.requirements)) {
        failedCount = this.requirements.filter((req) => req.status !== 'passed' && req.status !== true).length;
      } else if (this.requirements && typeof this.requirements === 'object') {
        failedCount = Object.values(this.requirements).filter(
          (req) => req.status !== 'passed' && req.status !== true,
        ).length;
      }

      const $badge = this.icon.find('.pp-checklists-badge');

      if (failedCount > 0) {
        $badge.text(failedCount).show();
      } else {
        $badge.hide();
      }
    }

    setupValidationListeners() {
      // Listen for Elementor changes
      if (typeof elementor !== 'undefined' && elementor.channels) {
        // Listen for content changes
        elementor.channels.data.on('change', () => {
          this.scheduleValidation();
        });

        // Listen for preview changes
        elementor.channels.editor.on('change', () => {
          this.scheduleValidation();
        });
      }

      console.log('PublishPress Checklists: Validation listeners set up');
    }

    scheduleValidation() {
      // Debounce validation to avoid too many requests
      if (this.validationTimer) {
        clearTimeout(this.validationTimer);
      }

      this.validationTimer = setTimeout(() => {
        this.validateRequirements();
      }, 1000);
    }

    validateRequirements() {
      if (!this.requirements || this.requirements.length === 0) {
        return;
      }

      console.log('PublishPress Checklists: Validating requirements...');

      // Get current Elementor data
      const elementorData = this.getElementorData();

      // Make AJAX request to validate
      $.ajax({
        url: ppChecklistsElementor.ajaxUrl,
        type: 'POST',
        data: {
          action: 'pp_checklists_elementor_validate',
          nonce: ppChecklistsElementor.nonce,
          post_id: ppChecklistsElementor.postId,
          elementor_data: JSON.stringify(elementorData),
        },
        success: (response) => {
          console.log('PublishPress Checklists: Validation results:', response.data);
          this.updateRequirementStatuses(response.data);
        },
        error: (xhr, status, error) => {
          console.error('PublishPress Checklists: Validation error:', error);
        },
      });
    }

    getElementorData() {
      // Get current Elementor document data
      if (typeof elementor !== 'undefined' && elementor.documents && elementor.documents.getCurrent()) {
        return elementor.documents.getCurrent().container.settings.toJSON();
      }
      return {};
    }

    updateRequirementStatuses(validationResults) {
      if (!validationResults || !Array.isArray(validationResults)) {
        return;
      }

      validationResults.forEach((result) => {
        const $req = this.panel.find(`[data-id="${result.id}"]`);
        if ($req.length === 0) return;

        // Update status class
        $req.removeClass('status-yes status-no');
        $req.addClass(result.status === 'passed' ? 'status-yes' : 'status-no');

        // Update icon
        const $icon = $req.find('.status-icon');
        $icon.removeClass('dashicons-yes dashicons-no');
        $icon.addClass(result.status === 'passed' ? 'dashicons-yes' : 'dashicons-no');

        // Update requirement in our array
        const reqIndex = this.requirements.findIndex((req) => req.id === result.id);
        if (reqIndex !== -1) {
          this.requirements[reqIndex].status = result.status;
        }
      });

      // Update badge count
      this.updateBadgeCount();
    }

    showError(message) {
      const $loading = this.panel.find('.pp-checklists-loading');
      $loading.html(`<p style="color: red;">${message}</p>`);
    }
  }

  // Initialize when DOM is ready
  $(document).ready(function () {
    // Only initialize in Elementor editor
    if (window.location.href.includes('action=elementor')) {
      new PublishPressChecklistsElementor();
    }
  });
})(jQuery);
