/**
 * PublishPress Checklists - Elementor Validation
 */
(function($) {
    'use strict';

    class ElementorValidation {
        constructor() {
            this.init();
        }

        init() {
            // Wait for Elementor to be ready
            if (typeof elementor !== 'undefined') {
                this.setupValidation();
            } else {
                $(document).ready(() => {
                    setTimeout(() => this.init(), 1000);
                });
            }
        }

        setupValidation() {
            console.log('PublishPress Checklists: Setting up Elementor validation');

            // Hook into Elementor's publish process
            if (elementor.documents && elementor.documents.getCurrent()) {
                this.hookIntoPublishProcess();
            }

            // Set up real-time validation
            this.setupRealTimeValidation();
        }

        hookIntoPublishProcess() {
            // Override the publish button behavior
            const originalPublish = elementor.documents.getCurrent().save;
            
            elementor.documents.getCurrent().save = (...args) => {
                console.log('PublishPress Checklists: Intercepting publish/save');
                
                // Check if we should validate before saving
                this.validateBeforeSave().then((canSave) => {
                    if (canSave) {
                        // Proceed with original save
                        originalPublish.apply(elementor.documents.getCurrent(), args);
                    }
                });
            };
        }

        setupRealTimeValidation() {
            // Listen for content changes
            if (elementor.channels) {
                elementor.channels.data.on('change', () => {
                    this.triggerValidation();
                });
            }
        }

        validateBeforeSave() {
            return new Promise((resolve) => {
                // Get current validation status
                $.ajax({
                    url: ppChecklistsElementor.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'pp_checklists_elementor_validate',
                        nonce: ppChecklistsElementor.nonce,
                        post_id: ppChecklistsElementor.postId,
                        elementor_data: JSON.stringify(this.getElementorData())
                    },
                    success: (response) => {
                        const results = response.data || [];
                        const failedRequired = results.filter(r => r.status !== 'passed' && r.required);
                        
                        if (failedRequired.length > 0) {
                            this.showValidationDialog(failedRequired, resolve);
                        } else {
                            resolve(true);
                        }
                    },
                    error: () => {
                        // If validation fails, allow save but warn
                        console.warn('PublishPress Checklists: Validation check failed');
                        resolve(true);
                    }
                });
            });
        }

        showValidationDialog(failedRequirements, callback) {
            const failedList = failedRequirements.map(req => `• ${req.label}`).join('\n');
            
            const message = `The following required checklist items are not complete:\n\n${failedList}\n\nDo you want to publish anyway?`;
            
            if (confirm(message)) {
                callback(true);
            } else {
                callback(false);
            }
        }

        triggerValidation() {
            // Trigger validation in the panel if it exists
            if (window.ppChecklistsElementorPanel) {
                window.ppChecklistsElementorPanel.scheduleValidation();
            }
        }

        getElementorData() {
            if (typeof elementor !== 'undefined' && elementor.documents && elementor.documents.getCurrent()) {
                return elementor.documents.getCurrent().container.settings.toJSON();
            }
            return {};
        }
    }

    // Initialize validation
    $(document).ready(function() {
        if (window.location.href.includes('action=elementor')) {
            new ElementorValidation();
        }
    });

})(jQuery);
