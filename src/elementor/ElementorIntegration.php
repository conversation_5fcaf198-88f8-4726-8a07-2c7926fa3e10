<?php

namespace PublishPress\ChecklistsPro\Elementor;

defined('ABSPATH') or die('No direct script access allowed.');

/**
 * Elementor Integration for PublishPress Checklists Pro
 */
class ElementorIntegration
{
    /**
     * Initialize the Elementor integration
     */
    public function __construct()
    {
        // Only initialize if Elementor is active
        if (!$this->isElementorActive()) {
            return;
        }

        $this->initHooks();
    }

    /**
     * Initialize hooks
     */
    private function initHooks()
    {
        add_action('elementor/editor/after_enqueue_scripts', [$this, 'enqueueElementorScripts']);
        add_action('wp_ajax_pp_checklists_elementor_get_requirements', [$this, 'getElementorRequirements']);
        add_action('wp_ajax_pp_checklists_elementor_validate', [$this, 'handleElementorValidation']);
    }

    /**
     * Enqueue scripts for Elementor editor
     */
    public function enqueueElementorScripts()
    {
        if (!$this->isElementorEditor()) {
            return;
        }

        $post_id = get_the_ID();
        if (!$post_id) {
            return;
        }

        // Enqueue our custom Elementor panel script
        wp_enqueue_script(
            'pp-checklists-elementor-panel',
            plugins_url('assets/js/elementor-panel.js', __FILE__),
            ['jquery', 'elementor-editor'],
            PPCHPRO_VERSION,
            true
        );

        // Enqueue validation script
        wp_enqueue_script(
            'pp-checklists-elementor-validation',
            plugins_url('assets/js/elementor-validation.js', __FILE__),
            ['jquery', 'elementor-editor', 'pp-checklists-elementor-panel'],
            PPCHPRO_VERSION,
            true
        );

        // Enqueue styles
        wp_enqueue_style(
            'pp-checklists-elementor-panel',
            plugins_url('assets/css/elementor-panel.css', __FILE__),
            ['elementor-editor'],
            PPCHPRO_VERSION
        );

        // Localize script with data
        wp_localize_script('pp-checklists-elementor-panel', 'ppChecklistsElementor', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pp_checklists_elementor_nonce'),
            'postId' => $post_id,
            'postType' => get_post_type($post_id),
            'strings' => [
                'loading' => __('Loading checklists...', 'publishpress-checklists-pro'),
                'noTasks' => __('No checklist tasks configured.', 'publishpress-checklists-pro'),
                'required' => __('Required', 'publishpress-checklists-pro'),
                'checklists' => __('Checklists', 'publishpress-checklists-pro'),
            ]
        ]);
    }

    /**
     * Check if we're in Elementor editor
     */
    private function isElementorEditor()
    {
        return defined('ELEMENTOR_VERSION') && 
               \Elementor\Plugin::$instance->editor->is_edit_mode();
    }

    /**
     * Check if Elementor is active
     */
    private function isElementorActive()
    {
        return defined('ELEMENTOR_VERSION') && class_exists('\Elementor\Plugin');
    }

    /**
     * Get requirements for Elementor via AJAX
     */
    public function getElementorRequirements()
    {
        check_ajax_referer('pp_checklists_elementor_nonce', 'nonce');

        $post_id = intval($_POST['post_id'] ?? 0);
        if (!$post_id) {
            wp_die('Invalid post ID');
        }

        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Get requirements using the existing filter system
        $requirements = [];
        $requirements = apply_filters('publishpress_checklists_requirement_list', $requirements, $post);
        
        // Use the existing method to rearrange requirements if available
        if (class_exists('PPCH_Checklists')) {
            $checklists_instance = \PPCH_Checklists::instance();
            if ($checklists_instance && method_exists($checklists_instance, 'rearrange_requirement_array')) {
                $requirements = $checklists_instance->rearrange_requirement_array($requirements);
            }
        }
        
        wp_send_json_success($requirements);
    }

    /**
     * Handle AJAX validation request from Elementor
     */
    public function handleElementorValidation()
    {
        check_ajax_referer('pp_checklists_elementor_nonce', 'nonce');

        $post_id = intval($_POST['post_id'] ?? 0);
        if (!$post_id) {
            wp_die('Invalid post ID');
        }

        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Get requirements using the existing filter system
        $requirements = [];
        $requirements = apply_filters('publishpress_checklists_requirement_list', $requirements, $post);
        
        // Use the existing method to rearrange requirements if available
        if (class_exists('PPCH_Checklists')) {
            $checklists_instance = \PPCH_Checklists::instance();
            if ($checklists_instance && method_exists($checklists_instance, 'rearrange_requirement_array')) {
                $requirements = $checklists_instance->rearrange_requirement_array($requirements);
            }
        }

        wp_send_json_success($requirements);
    }
}
